#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * JSON to SQL Converter
 * Converts all JSON files in the current directory to SQL INSERT statements
 */

class JsonToSqlConverter {
    constructor() {
        this.outputDir = './sql_output';
        this.combinedSqlFile = path.join(this.outputDir, 'all_tables.sql');
    }

    /**
     * Main conversion function
     */
    async convertAllJsonFiles() {
        try {
            // Create output directory
            this.ensureOutputDirectory();

            // Find all JSON files in current directory
            const jsonFiles = this.findJsonFiles();
            
            if (jsonFiles.length === 0) {
                console.log('No JSON files found in the current directory.');
                return;
            }

            console.log(`Found ${jsonFiles.length} JSON files:`, jsonFiles);

            let combinedSql = this.generateSqlHeader();

            // Process each JSON file
            for (const jsonFile of jsonFiles) {
                console.log(`\nProcessing ${jsonFile}...`);
                
                const sqlContent = await this.convertJsonFile(jsonFile);
                
                if (sqlContent) {
                    // Write individual SQL file
                    const sqlFileName = this.getSqlFileName(jsonFile);
                    const sqlFilePath = path.join(this.outputDir, sqlFileName);
                    fs.writeFileSync(sqlFilePath, sqlContent);
                    console.log(`✓ Created ${sqlFilePath}`);

                    // Add to combined SQL
                    combinedSql += `\n-- ========================================\n`;
                    combinedSql += `-- Data from ${jsonFile}\n`;
                    combinedSql += `-- ========================================\n\n`;
                    combinedSql += sqlContent + '\n';
                }
            }

            // Write combined SQL file
            fs.writeFileSync(this.combinedSqlFile, combinedSql);
            console.log(`\n✓ Created combined file: ${this.combinedSqlFile}`);
            
            console.log('\n🎉 Conversion completed successfully!');
            console.log(`📁 Output directory: ${this.outputDir}`);

        } catch (error) {
            console.error('❌ Error during conversion:', error.message);
            process.exit(1);
        }
    }

    /**
     * Find all JSON files in current directory
     */
    findJsonFiles() {
        return fs.readdirSync('.')
            .filter(file => file.endsWith('.json'))
            .filter(file => fs.statSync(file).isFile());
    }

    /**
     * Ensure output directory exists
     */
    ensureOutputDirectory() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * Convert a single JSON file to SQL
     */
    async convertJsonFile(jsonFile) {
        try {
            const jsonContent = fs.readFileSync(jsonFile, 'utf8');
            const data = JSON.parse(jsonContent);

            if (!Array.isArray(data) || data.length === 0) {
                console.log(`⚠️  Skipping ${jsonFile}: Not an array or empty`);
                return null;
            }

            const tableName = this.getTableName(jsonFile);
            const columns = this.analyzeColumns(data);
            
            let sql = this.generateSqlHeader(jsonFile);
            sql += this.generateCreateTableSql(tableName, columns);
            sql += this.generateInsertSql(tableName, data, columns);

            return sql;

        } catch (error) {
            console.error(`❌ Error processing ${jsonFile}:`, error.message);
            return null;
        }
    }

    /**
     * Get table name from JSON filename
     */
    getTableName(jsonFile) {
        return path.basename(jsonFile, '.json').toLowerCase();
    }

    /**
     * Get SQL filename from JSON filename
     */
    getSqlFileName(jsonFile) {
        const baseName = path.basename(jsonFile, '.json');
        return `${baseName}.sql`;
    }

    /**
     * Analyze JSON data to determine column structure
     */
    analyzeColumns(data) {
        const columns = new Map();
        
        // Analyze all records to get complete column information
        data.forEach(record => {
            Object.keys(record).forEach(key => {
                if (!columns.has(key)) {
                    columns.set(key, {
                        name: key,
                        type: this.inferSqlType(record[key]),
                        nullable: false
                    });
                } else {
                    // Check if this column can be null
                    if (record[key] === null || record[key] === undefined) {
                        columns.get(key).nullable = true;
                    }
                }
            });
        });

        return Array.from(columns.values());
    }

    /**
     * Infer SQL data type from JavaScript value
     */
    inferSqlType(value) {
        if (value === null || value === undefined) {
            return 'TEXT'; // Default for null values
        }
        
        if (typeof value === 'number') {
            return Number.isInteger(value) ? 'INTEGER' : 'DECIMAL(10,2)';
        }
        
        if (typeof value === 'boolean') {
            return 'BOOLEAN';
        }
        
        if (typeof value === 'string') {
            // Check if it's a date string
            if (this.isDateString(value)) {
                return 'TIMESTAMP';
            }
            return 'TEXT';
        }
        
        return 'TEXT'; // Default fallback
    }

    /**
     * Check if string is a date
     */
    isDateString(str) {
        return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(str);
    }

    /**
     * Generate SQL header comment
     */
    generateSqlHeader(jsonFile = null) {
        const timestamp = new Date().toISOString();
        let header = `-- ========================================\n`;
        header += `-- SQL Generated from JSON\n`;
        if (jsonFile) {
            header += `-- Source: ${jsonFile}\n`;
        }
        header += `-- Generated: ${timestamp}\n`;
        header += `-- ========================================\n\n`;
        return header;
    }

    /**
     * Generate CREATE TABLE SQL statement
     */
    generateCreateTableSql(tableName, columns) {
        let sql = `-- Create table: ${tableName}\n`;
        sql += `DROP TABLE IF EXISTS ${tableName};\n`;
        sql += `CREATE TABLE ${tableName} (\n`;
        
        const columnDefinitions = columns.map(col => {
            let def = `    ${col.name} ${col.type}`;
            if (!col.nullable && col.name === 'id') {
                def += ' PRIMARY KEY';
            } else if (!col.nullable) {
                def += ' NOT NULL';
            }
            return def;
        });
        
        sql += columnDefinitions.join(',\n');
        sql += `\n);\n\n`;
        
        return sql;
    }

    /**
     * Generate INSERT SQL statements
     */
    generateInsertSql(tableName, data, columns) {
        if (data.length === 0) return '';

        let sql = `-- Insert data into ${tableName}\n`;
        
        const columnNames = columns.map(col => col.name);
        sql += `INSERT INTO ${tableName} (${columnNames.join(', ')}) VALUES\n`;
        
        const valueRows = data.map((record, index) => {
            const values = columnNames.map(colName => {
                return this.formatSqlValue(record[colName]);
            });
            
            const isLast = index === data.length - 1;
            return `    (${values.join(', ')})${isLast ? ';' : ','}`;
        });
        
        sql += valueRows.join('\n') + '\n\n';
        
        return sql;
    }

    /**
     * Format a JavaScript value for SQL
     */
    formatSqlValue(value) {
        if (value === null || value === undefined) {
            return 'NULL';
        }
        
        if (typeof value === 'number') {
            return value.toString();
        }
        
        if (typeof value === 'boolean') {
            return value ? 'TRUE' : 'FALSE';
        }
        
        if (typeof value === 'string') {
            // Escape single quotes and wrap in quotes
            return `'${value.replace(/'/g, "''")}'`;
        }
        
        // Fallback: convert to string and escape
        return `'${String(value).replace(/'/g, "''")}'`;
    }
}

// Main execution
if (require.main === module) {
    const converter = new JsonToSqlConverter();
    converter.convertAllJsonFiles();
}

module.exports = JsonToSqlConverter;
