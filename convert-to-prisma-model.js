/**
 * <PERSON><PERSON><PERSON> to convert scraped data to match the new Prisma Product model with SubCategory support
 *
 * This script reads JSON files from the "id" folder and converts them
 * to match the exact Prisma model structure with proper field names,
 * data types, and SubCategory relationships.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const INPUT_DIR = 'id'; // Directory containing input JSON files
const OUTPUT_DIR = 'prisma'; // Directory to save output JSON files
const OUTPUT_FILE = 'prisma_products.json'; // Single output file

/**
 * Read all JSON files from a directory and combine their data
 * @param {string} directory - Directory containing JSON files
 * @returns {Array} - Combined array of products from all files
 */
function readAllJsonFiles(directory) {
  console.log(`Reading all JSON files from ${directory}...`);

  try {
    // Get all JSON files in the directory
    const files = fs.readdirSync(directory);
    const jsonFiles = files.filter(file => file.endsWith('.json'));

    console.log(`Found ${jsonFiles.length} JSON files`);

    // Sort files to ensure consistent processing order
    jsonFiles.sort();

    // Combine data from all files
    let allProducts = [];

    for (const file of jsonFiles) {
      const filePath = path.join(directory, file);
      console.log(`Reading file: ${filePath}`);

      const data = fs.readFileSync(filePath, 'utf8');
      const products = JSON.parse(data);

      console.log(`  Found ${products.length} products in ${file}`);
      allProducts = allProducts.concat(products);
    }

    console.log(`Total products from all files: ${allProducts.length}`);
    return allProducts;

  } catch (error) {
    console.error(`Error reading JSON files: ${error.message}`);
    return [];
  }
}

/**
 * Convert product data to match new Prisma model with SubCategory support
 * @param {Object} product - Original product object
 * @param {number} index - Index for auto-incrementing ID
 * @param {Object} categoryMapping - Category name to ID mapping
 * @param {Object} subCategoryMapping - SubCategory name to ID mapping
 * @param {Object} supplierMapping - Supplier name to ID mapping
 * @returns {Object} - Product object matching Prisma model
 */
function convertToPrismaModel(product, index, categoryMapping, subCategoryMapping, supplierMapping) {
  // Convert price from cents to decimal if needed
  let price = product.price;
  let buying_price = product.buying_price;

  // If prices are in cents (greater than 1000), convert to decimal
  if (price > 1000) {
    price = parseFloat((price / 100).toFixed(2));
  }
  if (buying_price > 1000) {
    buying_price = parseFloat((buying_price / 100).toFixed(2));
  }

  // Convert product_quantity from text to number
  let product_quantity = 0;
  if (typeof product.product_quantity === 'string') {
    if (product.product_quantity.toLowerCase().includes('in stock')) {
      product_quantity = 10; // Default stock quantity
    } else if (product.product_quantity.toLowerCase().includes('out of stock')) {
      product_quantity = 0;
    } else {
      // Try to parse as number
      const parsed = parseInt(product.product_quantity);
      product_quantity = isNaN(parsed) ? 0 : parsed;
    }
  } else if (typeof product.product_quantity === 'number') {
    product_quantity = product.product_quantity;
  }

  // Create description from category and subcategory
  const description = product.category_name && product.subcategory
    ? `${product.category_name} - ${product.subcategory}`
    : product.description || null;

  // Get category and subcategory IDs
  const categoryId = categoryMapping[product.category_name] || null;
  const subCategoryKey = `${product.category_name}|${product.subcategory}`;
  const subCategoryId = subCategoryMapping[subCategoryKey] || null;
  const supplierId = supplierMapping[product.supplier_id] || null;

  // Convert to Prisma model structure
  return {
    id: index + 1, // Auto-incrementing ID starting from 1
    sku: product.sku ? product.sku.toString() : '', // Ensure SKU is string
    name: product.name || product.product_name || 'Unknown Product',
    description: description,
    categoryId: categoryId,
    subCategoryId: subCategoryId,
    brand: product.brand || null,
    price: price,
    buying_price: buying_price,
    image_url: product.image_url || null,
    product_url: product.product_url || null,
    product_quantity: product_quantity,
    is_active: true,
    featured: false,
    sortOrder: 0,
    supplierId: supplierId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

/**
 * Extract unique categories and subcategories from products
 * @param {Array} products - Array of product objects
 * @returns {Object} - Object with categories and subcategories
 */
function extractCategoriesAndSubCategories(products) {
  const categories = new Set();
  const categorySubCategories = {};

  products.forEach(product => {
    const categoryName = product.category_name;
    const subCategoryName = product.subcategory;

    if (categoryName) {
      categories.add(categoryName);

      if (subCategoryName) {
        if (!categorySubCategories[categoryName]) {
          categorySubCategories[categoryName] = new Set();
        }
        categorySubCategories[categoryName].add(subCategoryName);
      }
    }
  });

  return {
    categories: Array.from(categories),
    categorySubCategories: categorySubCategories
  };
}

/**
 * Extract unique suppliers from products
 * @param {Array} products - Array of product objects
 * @returns {Array} - Array of unique suppliers
 */
function extractSuppliers(products) {
  const suppliers = new Set();

  products.forEach(product => {
    if (product.supplier_id) {
      suppliers.add(product.supplier_id);
    }
  });

  return Array.from(suppliers);
}

/**
 * Generate category and subcategory mappings
 * @param {Array} categories - Array of category names
 * @param {Object} categorySubCategories - Object mapping categories to their subcategories
 * @returns {Object} - Category and subcategory mappings
 */
function generateCategoryAndSubCategoryMappings(categories, categorySubCategories) {
  const categoryMapping = {};
  const categoryData = [];
  const subCategoryMapping = {};
  const subCategoryData = [];

  // Create categories
  categories.forEach((category, index) => {
    const categoryId = index + 1;
    categoryMapping[category] = categoryId;

    categoryData.push({
      id: categoryId,
      name: category,
      description: `All ${category} items`,
      sortOrder: index,
      parentId: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  });

  // Create subcategories
  let subCategoryId = 1;
  Object.keys(categorySubCategories).forEach(categoryName => {
    const categoryId = categoryMapping[categoryName];
    if (categoryId) {
      let sortOrder = 0;
      categorySubCategories[categoryName].forEach(subCategoryName => {
        const key = `${categoryName}|${subCategoryName}`;
        subCategoryMapping[key] = subCategoryId;

        subCategoryData.push({
          id: subCategoryId,
          name: subCategoryName,
          description: `${subCategoryName} items in ${categoryName}`,
          sortOrder: sortOrder,
          categoryId: categoryId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        subCategoryId++;
        sortOrder++;
      });
    }
  });

  return {
    categoryMapping,
    categoryData,
    subCategoryMapping,
    subCategoryData
  };
}

/**
 * Generate supplier mapping data
 * @param {Array} suppliers - Array of supplier names
 * @returns {Object} - Supplier mapping data
 */
function generateSupplierMapping(suppliers) {
  const supplierMapping = {};
  const supplierData = [];

  suppliers.forEach((supplier, index) => {
    const supplierId = index + 1;
    supplierMapping[supplier] = supplierId;

    supplierData.push({
      id: supplierId,
      name: supplier,
      contactName: `${supplier} Contact`,
      email: `contact@${supplier.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`,
      phone: '+92-000-0000000',
      address: null, // Will be handled by Address model
      addressId: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  });

  return { supplierMapping, supplierData };
}

/**
 * Main function to convert data to Prisma model
 */
function convertToPrisma() {
  console.log('Starting conversion to new Prisma model with SubCategory support...');

  try {
    // Create output directory if it doesn't exist
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
      console.log(`Created output directory: ${OUTPUT_DIR}`);
    }

    // Read all products from JSON files
    const allProducts = readAllJsonFiles(INPUT_DIR);

    if (allProducts.length === 0) {
      console.error('No products found in the JSON files');
      return;
    }

    console.log(`Found ${allProducts.length} products`);

    // Extract categories, subcategories and suppliers
    const { categories, categorySubCategories } = extractCategoriesAndSubCategories(allProducts);
    const suppliers = extractSuppliers(allProducts);

    console.log(`Found ${categories.length} categories`);
    console.log(`Found ${Object.keys(categorySubCategories).reduce((total, cat) => total + categorySubCategories[cat].size, 0)} subcategories`);
    console.log(`Found ${suppliers.length} suppliers`);

    // Generate mappings
    const { categoryMapping, categoryData, subCategoryMapping, subCategoryData } =
      generateCategoryAndSubCategoryMappings(categories, categorySubCategories);
    const { supplierMapping, supplierData } = generateSupplierMapping(suppliers);

    // Convert products to Prisma model with proper mappings
    const prismaProducts = allProducts.map((product, index) =>
      convertToPrismaModel(product, index, categoryMapping, subCategoryMapping, supplierMapping)
    );

    // Save products
    const productsOutputPath = path.join(OUTPUT_DIR, OUTPUT_FILE);
    fs.writeFileSync(productsOutputPath, JSON.stringify(prismaProducts, null, 2));
    console.log(`Saved ${prismaProducts.length} products to ${productsOutputPath}`);

    // Save categories
    const categoriesOutputPath = path.join(OUTPUT_DIR, 'categories.json');
    fs.writeFileSync(categoriesOutputPath, JSON.stringify(categoryData, null, 2));
    console.log(`Saved ${categoryData.length} categories to ${categoriesOutputPath}`);

    // Save subcategories
    const subCategoriesOutputPath = path.join(OUTPUT_DIR, 'subcategories.json');
    fs.writeFileSync(subCategoriesOutputPath, JSON.stringify(subCategoryData, null, 2));
    console.log(`Saved ${subCategoryData.length} subcategories to ${subCategoriesOutputPath}`);

    // Save suppliers
    const suppliersOutputPath = path.join(OUTPUT_DIR, 'suppliers.json');
    fs.writeFileSync(suppliersOutputPath, JSON.stringify(supplierData, null, 2));
    console.log(`Saved ${supplierData.length} suppliers to ${suppliersOutputPath}`);

    console.log('Conversion to new Prisma model completed successfully!');
    console.log('Generated files:');
    console.log(`- ${productsOutputPath}`);
    console.log(`- ${categoriesOutputPath}`);
    console.log(`- ${subCategoriesOutputPath}`);
    console.log(`- ${suppliersOutputPath}`);

  } catch (error) {
    console.error(`Error converting to Prisma model: ${error.message}`);
  }
}

// Execute the main function
convertToPrisma();
