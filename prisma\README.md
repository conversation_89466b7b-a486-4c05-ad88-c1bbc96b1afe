# JSON to SQL Converter

A JavaScript script that automatically converts JSON files to SQL CREATE TABLE and INSERT statements.

## Features

- **Automatic Detection**: Finds all `.json` files in the current directory
- **Smart Type Inference**: Automatically detects data types (INTEGER, DECIMAL, TEXT, BOOLEAN, TIMESTAMP)
- **SQL Generation**: Creates both CREATE TABLE and INSERT statements
- **Multiple Output Formats**: 
  - Individual SQL files for each JSON file
  - Combined SQL file with all tables
- **Proper SQL Escaping**: Handles special characters and quotes in data
- **Null Value Support**: Correctly handles null/undefined values
- **Primary Key Detection**: Automatically sets `id` fields as PRIMARY KEY

## Usage

### Basic Usage
```bash
node json-to-sql-converter.js
```

### Requirements
- Node.js (any recent version)
- JSON files in the current directory

## Output

The script creates a `sql_output` directory containing:

- `categories.sql` - SQL for categories.json
- `subcategories.sql` - SQL for subcategories.json  
- `suppliers.sql` - SQL for suppliers.json
- `prisma_products.sql` - SQL for prisma_products.json
- `all_tables.sql` - Combined SQL file with all tables

## Example Output

For a JSON file like:
```json
[
  {
    "id": 1,
    "name": "Food Cupboard",
    "description": "All Food Cupboard items",
    "sortOrder": 0,
    "parentId": null,
    "createdAt": "2025-05-26T03:05:32.644Z",
    "updatedAt": "2025-05-26T03:05:32.644Z"
  }
]
```

The script generates:
```sql
-- Create table: categories
DROP TABLE IF EXISTS categories;
CREATE TABLE categories (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    sortOrder INTEGER NOT NULL,
    parentId TEXT,
    createdAt TIMESTAMP NOT NULL,
    updatedAt TIMESTAMP NOT NULL
);

-- Insert data into categories
INSERT INTO categories (id, name, description, sortOrder, parentId, createdAt, updatedAt) VALUES
    (1, 'Food Cupboard', 'All Food Cupboard items', 0, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z');
```

## Data Type Mapping

| JSON Type | SQL Type |
|-----------|----------|
| Integer numbers | `INTEGER` |
| Decimal numbers | `DECIMAL(10,2)` |
| Strings | `TEXT` |
| Booleans | `BOOLEAN` |
| Date strings (ISO format) | `TIMESTAMP` |
| null/undefined | `NULL` |

## File Structure

```
├── json-to-sql-converter.js    # Main script
├── categories.json             # Input JSON files
├── subcategories.json
├── suppliers.json
├── prisma_products.json
└── sql_output/                 # Generated SQL files
    ├── categories.sql
    ├── subcategories.sql
    ├── suppliers.sql
    ├── prisma_products.sql
    └── all_tables.sql
```

## Error Handling

The script includes error handling for:
- Invalid JSON files
- Empty or non-array JSON data
- File system errors
- Missing permissions

## Customization

You can modify the script to:
- Change output directory (modify `this.outputDir`)
- Adjust data type inference logic (modify `inferSqlType()`)
- Change table naming convention (modify `getTableName()`)
- Add custom SQL formatting (modify generation methods)

## Notes

- The script assumes JSON files contain arrays of objects
- All objects in an array should have similar structure
- The `id` field is automatically treated as PRIMARY KEY
- String values are properly escaped for SQL injection prevention
- Large datasets are handled efficiently with streaming writes
