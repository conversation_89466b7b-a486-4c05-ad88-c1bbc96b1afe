-- ========================================
-- SQL Generated from JSON
-- Source: categories.json
-- Generated: 2025-05-30T06:45:05.812Z
-- ========================================

-- Create table: categories
DROP TABLE IF EXISTS categories;
CREATE TABLE categories (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    sortOrder INTEGER NOT NULL,
    parentId TEXT,
    createdAt TIMESTAMP NOT NULL,
    updatedAt TIMESTAMP NOT NULL
);

-- Insert data into categories
INSERT INTO categories (id, name, description, sortOrder, parentId, createdAt, updatedAt) VALUES
    (1, 'Food Cupboard', 'All Food Cupboard items', 0, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (2, 'Home & Entertainment', 'All Home & Entertainment items', 1, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (3, 'Health & Beauty', 'All Health & Beauty items', 2, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (4, 'Frozen Food', 'All Frozen Food items', 3, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (5, 'Drinks', 'All Drinks items', 4, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (6, 'Fresh Food & Dairy', 'All Fresh Food & Dairy items', 5, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (7, 'Pharmacy', 'All Pharmacy items', 6, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (8, 'Cosmetic Range', 'All Cosmetic Range items', 7, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (9, 'Perfumery', 'All Perfumery items', 8, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (10, 'Household', 'All Household items', 9, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (11, 'Electronics', 'All Electronics items', 10, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (12, 'Baby', 'All Baby items', 11, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z'),
    (13, 'Pet', 'All Pet items', 12, NULL, '2025-05-26T03:05:32.644Z', '2025-05-26T03:05:32.644Z');

