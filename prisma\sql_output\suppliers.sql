-- ========================================
-- SQL Generated from JSON
-- Source: suppliers.json
-- Generated: 2025-05-30T06:45:05.925Z
-- ========================================

-- Create table: suppliers
DROP TABLE IF EXISTS suppliers;
CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    contactName TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    address TEXT NOT NULL,
    addressId TEXT NOT NULL,
    createdAt TIMESTAMP NOT NULL,
    updatedAt TIMESTAMP NOT NULL
);

-- Insert data into suppliers
INSERT INTO suppliers (id, name, contactName, email, phone, address, addressId, createdAt, updatedAt) VALUES
    (1, 'SPRINGS', 'SPRINGS Contact', '<EMAIL>', '+92-000-0000000', NULL, NULL, '2025-05-26T03:05:32.646Z', '2025-05-26T03:05:32.646Z');

